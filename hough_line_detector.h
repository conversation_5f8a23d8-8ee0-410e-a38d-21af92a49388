#ifndef HOUGH_LINE_DETECTOR_H
#define HOUGH_LINE_DETECTOR_H

#include <opencv2/opencv.hpp>
#include <vector>

class HoughLineDetector {
public:
    struct LineSegment {
        cv::Point2f start;
        cv::Point2f end;
        float rho;
        float theta;
    };

    struct Rectangle {
        cv::Point2f topLeft;
        cv::Point2f topRight;
        cv::Point2f bottomLeft;
        cv::Point2f bottomRight;
    };

    HoughLineDetector(int threshold = 100, double rho = 1.0, double theta = CV_PI/180);
    
    bool detectLines(const cv::Mat& image);
    Rectangle getRectangle() const { return rectangle_; }
    std::vector<LineSegment> getLines() const { return detected_lines_; }
    
    void setThreshold(int threshold) { threshold_ = threshold; }
    void setRhoTheta(double rho, double theta) { rho_ = rho; theta_ = theta; }

private:
    cv::Point2f getIntersection(const LineSegment& line1, const LineSegment& line2);
    void classifyLines(const std::vector<cv::Vec2f>& lines);
    bool isHorizontal(float theta);
    bool isVertical(float theta);
    
    int threshold_;
    double rho_;
    double theta_;
    
    std::vector<LineSegment> detected_lines_;
    std::vector<LineSegment> horizontal_lines_;
    std::vector<LineSegment> vertical_lines_;
    Rectangle rectangle_;
};

#endif