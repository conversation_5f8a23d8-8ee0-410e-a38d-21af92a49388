#include "hough_line_detector.h"
#include <algorithm>
#include <cmath>

HoughLineDetector::HoughLineDetector(int threshold, double rho, double theta)
    : threshold_(threshold), rho_(rho), theta_(theta) {}

bool HoughLineDetector::detectLines(const cv::Mat& image) {
    if (image.empty()) return false;
    
    cv::Mat gray, edges;
    
    // 转换为灰度图
    if (image.channels() == 3) {
        cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    } else {
        gray = image.clone();
    }
    
    // Canny边缘检测
    cv::Canny(gray, edges, 50, 150, 3);
    

    std::vector<cv::Vec2f> lines;
    cv::HoughLines(edges, lines, rho_, theta_, threshold_);
    
    if (lines.size() < 4) return false;
    
    // 分类直线
    classifyLines(lines);
    
    if (horizontal_lines_.size() < 2 || vertical_lines_.size() < 2) {
        return false;
    }
    
    // 计算四个交点
    rectangle_.topLeft = getIntersection(horizontal_lines_[0], vertical_lines_[0]);
    rectangle_.topRight = getIntersection(horizontal_lines_[0], vertical_lines_[1]);
    rectangle_.bottomLeft = getIntersection(horizontal_lines_[1], vertical_lines_[0]);
    rectangle_.bottomRight = getIntersection(horizontal_lines_[1], vertical_lines_[1]);
    
    return true;
}

void HoughLineDetector::classifyLines(const std::vector<cv::Vec2f>& lines) {
    horizontal_lines_.clear();
    vertical_lines_.clear();
    detected_lines_.clear();
    
    for (const auto& line : lines) {
        float rho = line[0];
        float theta = line[1];
        
        LineSegment segment;
        segment.rho = rho;
        segment.theta = theta;
        
        // 计算直线的起点和终点用于显示
        double a = cos(theta), b = sin(theta);
        double x0 = a * rho, y0 = b * rho;
        segment.start = cv::Point2f(x0 + 1000*(-b), y0 + 1000*(a));
        segment.end = cv::Point2f(x0 - 1000*(-b), y0 - 1000*(a));
        
        detected_lines_.push_back(segment);
        
        if (isHorizontal(theta)) {
            horizontal_lines_.push_back(segment);
        } else if (isVertical(theta)) {
            vertical_lines_.push_back(segment);
        }
    }
    
    // 排序：水平线按y坐标，垂直线按x坐标
    std::sort(horizontal_lines_.begin(), horizontal_lines_.end(),
        [](const LineSegment& a, const LineSegment& b) {
            return a.rho * sin(a.theta) < b.rho * sin(b.theta);
        });
    
    std::sort(vertical_lines_.begin(), vertical_lines_.end(),
        [](const LineSegment& a, const LineSegment& b) {
            return a.rho * cos(a.theta) < b.rho * cos(b.theta);
        });
}

bool HoughLineDetector::isHorizontal(float theta) {
    return (theta < CV_PI/4) || (theta > 3*CV_PI/4);
}

bool HoughLineDetector::isVertical(float theta) {
    return (theta > CV_PI/4) && (theta < 3*CV_PI/4);
}

cv::Point2f HoughLineDetector::getIntersection(const LineSegment& line1, const LineSegment& line2) {
    float rho1 = line1.rho, theta1 = line1.theta;
    float rho2 = line2.rho, theta2 = line2.theta;
    
    float cos1 = cos(theta1), sin1 = sin(theta1);
    float cos2 = cos(theta2), sin2 = sin(theta2);
    
    float det = cos1 * sin2 - sin1 * cos2;
    if (abs(det) < 1e-6) {
        return cv::Point2f(-1, -1); 
    }
    
    float x = (sin2 * rho1 - sin1 * rho2) / det;
    float y = (cos1 * rho2 - cos2 * rho1) / det;
    
    return cv::Point2f(x, y);
}